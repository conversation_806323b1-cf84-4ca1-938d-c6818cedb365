from multiprocessing import Process, Value, Lock
import subprocess
import os
import yaml
import time
import threading
import argparse
import numpy as np
import pandas as pd  # ✅ 用于加载标准答案
from pathlib import Path
from clients.faiss_client import FaissClient
from data.load_openai_parquet import load_vectors_from_directory

def load_config(path="config.yaml"):
    with open(path, "r") as f:
        return yaml.safe_load(f)

def parse_args():
    parser = argparse.ArgumentParser(description="FAISS Benchmark")

    # 数据集路径
    parser.add_argument("--dataset", type=str, required=True,
                        help="Dataset folder name or full path (e.g., openai_large_5m)")

    # 索引类型
    parser.add_argument("--index", type=str, default="flat",
                        choices=["flat", "ivfflat", "ivfpq", "hnsw"],
                        help="Index type: flat, ivfflat, ivfpq, hnsw")

    # IVF & PQ 参数
    parser.add_argument("--nlist", type=int, default=1024,
                        help="Number of clusters for IVF (Recommended: 1024~16384)")
    parser.add_argument("--nprobe", type=int, default=10,
                        help="Number of probes at query time for IVF (Recommended: 1~128)")
    parser.add_argument("--m", type=int, default=64,
                        help="Number of PQ subvectors for IVFPQ (Recommended: 32~128)")

    # HNSW 参数
    parser.add_argument("--efConstruction", type=int, default=200,
                        help="HNSW build parameter (Recommended: 100~500)")
    parser.add_argument("--efSearch", type=int, default=64,
                        help="HNSW search parameter (Recommended: <= efConstruction)")

    # 预构建索引路径
    parser.add_argument("--prebuilt_index", type=str, default=None,
                        help="Path to prebuilt FAISS index file (skips training and building)")

    # 并发线程数
    parser.add_argument("--concurrent_threads", type=int, nargs="+", default=[1, 8, 32],
                        help="List of concurrent threads to test (e.g., 1 8 32)")

    # 固定时长测试
    parser.add_argument("--duration", type=int, default=0,
                        help="Benchmark duration in seconds (0 means fixed query count)")
    parser.add_argument("--check-interval", type=int, default=1000,
                        help="Check time every N queries in fixed-duration mode (default: 1000)")
    parser.add_argument("--monitor", action="store_true",
                        help="Enable per-thread monitoring during benchmark")
    return parser.parse_args()

def start_monitoring(thread_count, log_root):
    """启动监控脚本"""
    thread_log_dir = os.path.join(log_root, f"t{thread_count}")
    os.makedirs(thread_log_dir, exist_ok=True)

    os_metrics_cmd = [
        "sh", "/nas/brian.mao/os_metrics.sh",
        "-c", "benchmark.py", "-i", "1", "-t", "30",
        "-o", thread_log_dir
    ]
    perf_metrics_cmd = [
        "python3", "/nas/brian.mao/scripts-master/topdown/perf_metrics.py",
        "-C", "0-15", "--command", "sleep 5", "-ll","-o", os.path.join(thread_log_dir, "perf_metrics.log")
    ]

    os_log = open(f"{thread_log_dir}/os_metrics.log", "w")
    perf_log = open(f"{thread_log_dir}/perf_metrics.log", "w")

    os_proc = subprocess.Popen(os_metrics_cmd, stdout=os_log, stderr=subprocess.STDOUT)
    perf_proc = subprocess.Popen(perf_metrics_cmd, stdout=perf_log, stderr=subprocess.STDOUT)

    print(f"▶ Started monitoring for {thread_count} threads → {thread_log_dir}")
    return os_proc, perf_proc

def stop_monitoring(os_proc, perf_proc, thread_count):
    """停止监控脚本"""
    for proc in [os_proc, perf_proc]:
        try:
            proc.send_signal(signal.SIGTERM)
            proc.wait(timeout=5)
        except Exception:
            proc.kill()
    print(f"✅ Stopped monitoring for {thread_count} threads")

def compute_recall(results, ground_truth, topk):
    """计算召回率"""
    correct = 0
    total = len(results)
    for idx, retrieved in enumerate(results):
        gt_neighbors = set(ground_truth[idx][:topk])
        if len(gt_neighbors.intersection(set(retrieved))) > 0:
            correct += 1
    recall = (correct / total) * 100
    return recall

def search_worker(client, queries, topk, results, idx):
    _, indices = client.search(queries, topk=topk)
    results[idx] = indices

def benchmark_fixed_query(client, queries, topk, threads, ground_truth=None):
    """固定 query 数量的 Benchmark"""
    results = [None] * threads
    queries_split = np.array_split(queries, threads)

    start = time.time()
    thread_list = []
    for i in range(threads):
        t = threading.Thread(target=search_worker, args=(client, queries_split[i], topk, results, i))
        thread_list.append(t)
        t.start()
    for t in thread_list:
        t.join()
    elapsed = time.time() - start

    # 合并所有线程结果
    all_indices = np.vstack(results)
    qps = len(queries) / elapsed
    latency = elapsed / len(queries)

    recall = None
    if ground_truth is not None:
        recall = compute_recall(all_indices, ground_truth, topk)

    return qps, latency, recall

def benchmark_fixed_time(client, queries, topk, num_processes, duration_s, log_dir, monitor, check_interval=1000):
    """固定时长多进程测试，优化 time.time() 调用开销"""
    from multiprocessing import Process, Value, Lock

    total_queries = Value('i', 0)
    lock = Lock()
    
    log_dir = f"/tmp/faiss_monitor_logs/{time.strftime('%Y%m%d_%H%M%S')}"
    os_proc, perf_proc = (None, None)
    if monitor:
        os_proc, perf_proc = start_monitoring(num_processes, log_dir)
    start_time = time.time()
    end_time = start_time + duration_s

    def query_loop(shared_counter, lock, check_interval):
        rng = np.random.default_rng()
        local_count = 0
        iterations = 0
        while True:
            query = rng.choice(queries)
            client.search([query], topk=topk)
            local_count += 1
            iterations += 1

            if iterations % check_interval == 0:
                if time.time() >= end_time:
                    break

        with lock:
            shared_counter.value += local_count

    processes = []
    for _ in range(num_processes):
        p = Process(target=query_loop, args=(total_queries, lock, check_interval))
        processes.append(p)
        p.start()
    for p in processes:
        p.join()

    total_elapsed = time.time() - start_time
    
    if monitor:
        stop_monitoring(os_proc, perf_proc, num_processes)
    qps = total_queries.value / total_elapsed
    avg_latency = total_elapsed / total_queries.value if total_queries.value else 0
    return qps, avg_latency

def main():
    args = parse_args()
    cfg = load_config()

    client_params = cfg["client"]["params"]
    client_params["index_type"] = args.index.lower()

    # 添加预构建索引路径
    if args.prebuilt_index:
        client_params["prebuilt_index_path"] = args.prebuilt_index
        print(f"▶ Using prebuilt index: {args.prebuilt_index}")

    # 根据索引类型调整参数
    if args.index in ["ivfflat", "ivfpq"]:
        client_params["nlist"] = args.nlist
        client_params["nprobe"] = args.nprobe
        if args.index == "ivfpq":
            client_params["m"] = args.m
    elif args.index == "hnsw":
        client_params["efConstruction"] = args.efConstruction
        client_params["efSearch"] = args.efSearch
    monitor_log_root = f"/tmp/faiss_monitor_logs/{time.strftime('%Y%m%d_%H%M%S')}"

    print(f"▶ Benchmarking dataset: {args.dataset}")
    print(f"▶ Index: {args.index.upper()}, Threads: {args.concurrent_threads}")
    if args.duration > 0:
        print(f"▶ Running fixed-durationbenchmark: {args.duration} seconds")
    else:
        print("▶ Running fixed-query benchmark")

    bench_cfg = cfg["benchmark"]

    # 解析数据集路径
    dataset_dir = Path(args.dataset)
    if not dataset_dir.exists():
        base_path = Path("/nas/yvan.chen/milvus/dataset")
        dataset_dir = base_path / args.dataset
    if not dataset_dir.exists():
        raise FileNotFoundError(f"Dataset folder not found: {dataset_dir}")

    vectors, queries = load_vectors_from_directory(dataset_dir,
        num_train=bench_cfg["num_vectors"],
        num_test=bench_cfg["num_queries"]
    )

    print(f"✅ Loaded {vectors.shape[0]} train vectors (dim={vectors.shape[1]})")
    print(f"✅ Loaded {queries.shape[0]} query vectors (dim={queries.shape[1]})")
    # 加载标准答案 neighbors.parquet
    neighbors_file = dataset_dir / "neighbors.parquet"
    ground_truth = None
    if neighbors_file.exists() and args.duration == 0:  # 仅固定 query 模式计算 recall
        neighbors_df = pd.read_parquet(neighbors_file)
        possible_cols = ["neighbors", "ids", "ground_truth"]
        found_col = next((col for col in possible_cols if col in neighbors_df.columns), None)
        if found_col:
            ground_truth = neighbors_df[found_col].tolist()
            print(f"✅ Loaded ground truth from column: {found_col} for recall calculation.")
        else:
            print(f"⚠ No valid column found in {neighbors_file}. Skipping recall calculation.")
    elif args.duration > 0:
        print("⚠ Skipping recall calculation in fixed-duration mode.")

    print(f"▶ Initializing FAISS index: {args.index.upper()}")
    client = FaissClient(**client_params)

    print("▶ Building and inserting index...")
    insert_time = client.insert(vectors)
    print(f"✅ Insert done in {insert_time:.2f}s")
    log_root = f"./monitor_logs/{args.index}"

    print("▶ Search Benchmark:")
    for t in args.concurrent_threads:
        if args.duration > 0:
            qps, latency = benchmark_fixed_time(
                client, queries, bench_cfg["query_topk"], t, args.duration, log_root, args.monitor, check_interval=1000
            )

            print(f"{t} threads -> QPS: {qps:.2f} | Latency: {latency:.6f}s")
        else:
            qps, latency, recall = benchmark_fixed_query(client, queries, bench_cfg["query_topk"], t, ground_truth)
            if recall is not None:
                print(f"{t} threads -> QPS: {qps:.2f} | Latency: {latency:.6f}s | Recall: {recall:.2f}%")
            else:
                print(f"{t} threads -> QPS: {qps:.2f} | Latency: {latency:.6f}s")

if __name__ == "__main__":
    main()

