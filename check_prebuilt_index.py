#!/usr/bin/env python3
"""
检查预构建FAISS索引文件的信息
"""
import faiss
import os
import sys

def check_index_info(index_path):
    """检查索引文件信息"""
    if not os.path.exists(index_path):
        print(f"❌ 索引文件不存在: {index_path}")
        return False
    
    try:
        print(f"▶ 正在加载索引: {index_path}")
        index = faiss.read_index(index_path)
        
        print(f"✅ 索引加载成功!")
        print(f"   - 维度: {index.d}")
        print(f"   - 向量数量: {index.ntotal}")
        print(f"   - 索引类型: {type(index).__name__}")
        
        # 检查是否是IVF类型
        if hasattr(index, 'nlist'):
            print(f"   - nlist (聚类数): {index.nlist}")
        if hasattr(index, 'nprobe'):
            print(f"   - nprobe (当前): {index.nprobe}")
            
        # 检查是否是HNSW类型
        if hasattr(index, 'hnsw'):
            print(f"   - HNSW efSearch: {index.hnsw.efSearch}")
            print(f"   - HNSW max_level: {index.hnsw.max_level}")
            
        # 检查是否是PQ类型
        if hasattr(index, 'pq'):
            print(f"   - PQ M (子向量数): {index.pq.M}")
            print(f"   - PQ nbits: {index.pq.nbits}")
            
        return True
        
    except Exception as e:
        print(f"❌ 加载索引失败: {e}")
        return False

def find_possible_indexes():
    """查找可能的索引文件"""
    possible_paths = [
        "/nas/brian.mao/VectorDBBench2/faiss_hnsw_768d_10m.index",
        "/nas/brian.mao/VectorDBBench2/",
        "/nas/brian.mao/",
        "./",
    ]
    
    found_indexes = []
    
    for path in possible_paths:
        if os.path.isfile(path) and path.endswith('.index'):
            found_indexes.append(path)
        elif os.path.isdir(path):
            try:
                for file in os.listdir(path):
                    if file.endswith('.index') or file.endswith('.faiss'):
                        full_path = os.path.join(path, file)
                        found_indexes.append(full_path)
            except PermissionError:
                print(f"⚠ 无权限访问目录: {path}")
                
    return found_indexes

if __name__ == "__main__":
    if len(sys.argv) > 1:
        index_path = sys.argv[1]
        check_index_info(index_path)
    else:
        print("▶ 搜索可能的索引文件...")
        found = find_possible_indexes()
        
        if found:
            print(f"✅ 找到 {len(found)} 个索引文件:")
            for i, path in enumerate(found, 1):
                print(f"{i}. {path}")
                
            print("\n▶ 检查第一个索引文件的详细信息:")
            check_index_info(found[0])
        else:
            print("❌ 未找到任何索引文件")
            print("请检查以下路径是否存在索引文件:")
            print("- /nas/brian.mao/VectorDBBench2/")
            print("- /nas/brian.mao/")
