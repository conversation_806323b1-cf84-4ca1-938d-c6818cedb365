import faiss
import numpy as np
import os
import time

class FaissClient:
    def __init__(self, dim=1536, use_gpu=False, index_type="flat",
                 nlist=100, m=64, nprobe=10, efConstruction=200,
                 efSearch=64, index_path=None, prebuilt_index_path=None):
        self.dim = dim  # 使用传入的维度参数
        self.use_gpu = use_gpu
        self.index_type = index_type.lower()
        self.nlist = nlist
        self.m = m
        self.nprobe = nprobe
        self.efConstruction = efConstruction
        self.efSearch = efSearch
        self.index_path = index_path or f"faiss_{self.index_type}_{nlist}_{m}.faiss"
        self.prebuilt_index_path = prebuilt_index_path
        self.index = None

        # 优先使用预构建的索引
        if self.prebuilt_index_path and os.path.exists(self.prebuilt_index_path):
            print(f"▶ Loading prebuilt index from: {self.prebuilt_index_path}")
            self.index = faiss.read_index(self.prebuilt_index_path)
            # 自动检测索引维度
            if hasattr(self.index, 'd'):
                detected_dim = self.index.d
                if detected_dim != self.dim:
                    print(f"⚠ Dimension mismatch: config={self.dim}, index={detected_dim}. Using index dimension.")
                    self.dim = detected_dim

            # 设置搜索参数
            if self.index_type.startswith("ivf") or hasattr(self.index, 'nprobe'):
                self.index.nprobe = self.nprobe
                print(f"▶ Set nprobe to {self.nprobe}")
            if hasattr(self.index, "hnsw"):
                self.index.hnsw.efSearch = self.efSearch
                print(f"▶ Set efSearch to {self.efSearch}")
            if self.use_gpu:
                self.index = faiss.index_cpu_to_all_gpus(self.index)
            print(f"✅ Prebuilt index loaded successfully (dim={self.dim})")

        elif os.path.exists(self.index_path):
            print(f"▶ Loading existing index from: {self.index_path}")
            self.index = faiss.read_index(self.index_path)
            if self.index_type.startswith("ivf"):
                self.index.nprobe = self.nprobe
            if hasattr(self.index, "hnsw"):
                self.index.hnsw.efSearch = self.efSearch
            if self.use_gpu:
                self.index = faiss.index_cpu_to_all_gpus(self.index)
        else:
            print("▶ No saved index found. Will train and build from scratch.")

    def insert(self, vectors):
        vectors = np.array(vectors).astype('float32')
        if vectors.shape[1] != self.dim:
            raise ValueError(
                f"❌ Dataset dimension mismatch: expected {self.dim}, got {vectors.shape[1]}"
            )

        if self.index is not None:
            print("▶ Index already loaded. Skipping insert.")
            return 0

        start = time.time()

        if self.index_type == "flat":
            self.index = faiss.IndexFlatL2(self.dim)

        elif self.index_type == "ivfflat":
            quantizer = faiss.IndexFlatL2(self.dim)
            index = faiss.IndexIVFFlat(quantizer, self.dim, self.nlist)
            print(f"▶ Training IVFFlat index...")
            index.train(vectors[:min(100000, vectors.shape[0])])
            index.add(vectors)
            index.nprobe = self.nprobe
            self.index = index

        elif self.index_type == "ivfpq":
            quantizer = faiss.IndexFlatL2(self.dim)
            index = faiss.IndexIVFPQ(quantizer, self.dim, self.nlist, self.m, 8)
            print(f"▶ Training IVFPQ index...")
            index.train(vectors[:min(100000, vectors.shape[0])])
            index.add(vectors)
            index.nprobe = self.nprobe
            self.index = index

        elif self.index_type == "hnsw":
            print(f"▶ Creating HNSW index (efConstruction={self.efConstruction})...")
            self.index = faiss.IndexHNSWFlat(self.dim, 32)
            self.index.hnsw.efConstruction = self.efConstruction
            self.index.add(vectors)

        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")

        if self.use_gpu:
            self.index = faiss.index_cpu_to_all_gpus(self.index)

        faiss.write_index(
            faiss.index_gpu_to_cpu(self.index) if self.use_gpu else self.index,
            self.index_path
        )
        print(f"✅ Index saved to {self.index_path}")
        return time.time() - start

    def search(self, queries, topk=10):
        queries = np.array(queries).astype('float32')
        if self.index is None:
            raise RuntimeError("Index is not initialized. Did you forget to call insert()?")

        if hasattr(self.index, "hnsw"):
            self.index.hnsw.efSearch = self.efSearch

        start = time.time()
        distances, indices = self.index.search(queries, topk)
        elapsed = time.time() - start

        return elapsed, indices.tolist()

