import faiss
import numpy as np
import time
import os

class FaissClient:
    def __init__(self, dim=768, use_gpu=False, index_type="flat",
                 nlist=100, m=64, nprobe=10, efConstruction=200, index_path=None):
        self.dim = dim
        self.use_gpu = use_gpu
        self.index_type = index_type.lower()
        self.nlist = nlist
        self.m = m
        self.nprobe = nprobe
        self.efConstruction = efConstruction
        self.efSearch = efSearch
        self.index_path = index_path or f"faiss_{self.index_type}_{nlist}_{m}.faiss"
        self.index = None

        if os.path.exists(self.index_path):
            print(f"▶ Loading existing index from: {self.index_path}")
            self.index = faiss.read_index(self.index_path)
            if self.index_type.startswith("ivf"):
                self.index.nprobe = self.nprobe
            if self.use_gpu:
                self.index = faiss.index_cpu_to_all_gpus(self.index)
        else:
            print(f"▶ No saved index found. Will train and build from scratch.")

    def insert(self, vectors):
        vectors = np.array(vectors).astype('float32')
        if vectors.shape[1] != self.dim:
            raise ValueError(f"Vector dimension mismatch: expected {self.dim}, got {vectors.shape[1]}")

        if self.index is not None:
            print("▶ Index already loaded. Skipping insert.")
            return 0

        start = time.time()

        if self.index_type == "flat":
            self.index = faiss.IndexFlatL2(self.dim)

        elif self.index_type == "ivfflat":
            quantizer = faiss.IndexFlatL2(self.dim)
            index = faiss.IndexIVFFlat(quantizer, self.dim, self.nlist)
            index.train(vectors[:min(100000, vectors.shape[0])])
            index.add(vectors)
            index.nprobe = self.nprobe
            self.index = index

        elif self.index_type == "ivfpq":
            quantizer = faiss.IndexFlatL2(self.dim)
            index = faiss.IndexIVFPQ(quantizer, self.dim, self.nlist, self.m, 8)
            index.train(vectors[:min(100000, vectors.shape[0])])
            index.add(vectors)
            index.nprobe = self.nprobe
            self.index = index

        elif self.index_type == "hnsw":
            self.index = faiss.IndexHNSWFlat(self.dim, 32)
            self.index.hnsw.efConstruction = self.efConstruction
            self.index.add(vectors)

        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")

        if self.use_gpu:
            self.index = faiss.index_cpu_to_all_gpus(self.index)

        faiss.write_index(faiss.index_gpu_to_cpu(self.index) if self.use_gpu else self.index, self.index_path)
        print(f"✅ Index saved to {self.index_path}")
        return time.time() - start

    def search(self, queries, topk=10):
        queries = np.array(queries).astype('float32')
        if self.index is None:
            raise RuntimeError("Index is not initialized. Did you forget to call insert()?")

        # ✅ 设置 efSearch
        if hasattr(self.index, "hnsw"):
            self.index.hnsw.efSearch = self.efSearch  # 从 init 时保存的值
        return self.index.search(queries, topk)

