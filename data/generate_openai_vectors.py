from datasets import load_dataset
from langchain.embeddings import OpenAIEmbeddings
import time

def generate_real_vectors(n=1000):
    dataset = load_dataset("hotpot_qa", split="train[:{}]".format(n))
    embedder = OpenAIEmbeddings()

    texts = []
    for row in dataset:
        question = row["question"]
        context = row["context"]
        texts.append(question + " " + context[:300])  # 控制长度

    vectors = []
    print(f"Generating {len(texts)} embeddings from OpenAI...")

    for text in texts:
        try:
            vec = embedder.embed_query(text)
            vectors.append(vec)
        except Exception as e:
            print("Error:", e)
            continue
        time.sleep(0.5)  # 避免 API 限速

    return vectors

