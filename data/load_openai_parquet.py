import pandas as pd
import numpy as np
import os

def load_vectors_from_directory(dataset_dir, num_train=1000000, num_test=1000):
    vector_col = "emb"

    # 合并多个分片
    train_files = [f for f in os.listdir(dataset_dir) if f.startswith("shuffle_train")]
    train_files.sort()
    vectors = []
    for f in train_files:
        df = pd.read_parquet(os.path.join(dataset_dir, f))
        vectors.extend(df[vector_col].values)
        if len(vectors) >= num_train:
            break
    train_vectors = np.vstack(vectors[:num_train]).astype('float32')

    # 加载测试集
    test_df = pd.read_parquet(os.path.join(dataset_dir, "test.parquet"))
    test_vectors = np.vstack(test_df[vector_col].values[:num_test]).astype('float32')

    return train_vectors, test_vectors

